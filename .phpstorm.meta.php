<?php

/**
 * 自动生成的类型映射文件
 * 生成时间: 2025-08-12 07:40:49
 * 请勿手动修改
 */

namespace PHPSTORM_META {
    // D() 函数返回类型映射
    override(\D(0), map([
        'm_base' => \m_base::class,
        'm_black' => \m_black::class,
        'm_xm' => \m_xm::class,
        'm_admin_base' => \m_admin_base::class,
        'm_admin_config' => \m_admin_config::class,
        'm_admin_main' => \m_admin_main::class,
        'm_admin_qx' => \m_admin_qx::class,
        'm_admin_zh' => \m_admin_zh::class,
        'm_admin_bb_main' => \m_admin_bb_main::class,
        'm_admin_finance_base' => \m_admin_finance_base::class,
        'm_admin_finance_supplier' => \m_admin_finance_supplier::class,
        'm_admin_finance_tx' => \m_admin_finance_tx::class,
        'm_admin_pz_main' => \m_admin_pz_main::class,
        'm_admin_site_ads' => \m_admin_site_ads::class,
        'm_admin_site_article' => \m_admin_site_article::class,
        'm_admin_site_base' => \m_admin_site_base::class,
        'm_admin_site_category' => \m_admin_site_category::class,
        'm_admin_site_invitecode' => \m_admin_site_invitecode::class,
        'm_admin_site_notice' => \m_admin_site_notice::class,
        'm_admin_site_xieyi' => \m_admin_site_xieyi::class,
        'm_admin_site_znx' => \m_admin_site_znx::class,
        'm_admin_system_queue' => \m_admin_system_queue::class,
        'm_admin_user_main' => \m_admin_user_main::class,
        'm_admin_user_media' => \m_admin_user_media::class,
        'm_admin_xm_main' => \m_admin_xm_main::class,
        'm_admin_xm_nrk' => \m_admin_xm_nrk::class,
        'm_admin_xm_order' => \m_admin_xm_order::class,
        'm_admin_xm_price' => \m_admin_xm_price::class,
        'm_admin_xm_tag' => \m_admin_xm_tag::class,
        'm_admin_zd_tpl' => \m_admin_zd_tpl::class,
        'm_api_article_main' => \m_api_article_main::class,
        'm_api_article_xy' => \m_api_article_xy::class,
        'm_api_bb_main' => \m_api_bb_main::class,
        'm_api_order_main' => \m_api_order_main::class,
        'm_api_pz_main' => \m_api_pz_main::class,
        'm_api_site_ads' => \m_api_site_ads::class,
        'm_api_site_notice' => \m_api_site_notice::class,
        'm_api_user_main' => \m_api_user_main::class,
        'm_api_user_media' => \m_api_user_media::class,
        'm_api_user_qian' => \m_api_user_qian::class,
        'm_api_user_team' => \m_api_user_team::class,
        'm_api_xm_main' => \m_api_xm_main::class,
        'm_api_xm_notice' => \m_api_xm_notice::class,
        'm_api_xm_nrk' => \m_api_xm_nrk::class,
        'm_api_xm_price' => \m_api_xm_price::class,
        'm_comm_auth' => \m_comm_auth::class,
        'm_comm_base' => \m_comm_base::class,
        'm_comm_cat' => \m_comm_cat::class,
        'm_comm_lang' => \m_comm_lang::class,
        'm_msg_email' => \m_msg_email::class,
        'm_msg_znx' => \m_msg_znx::class,
        'm_qian_base' => \m_qian_base::class,
        'm_qian_dd' => \m_qian_dd::class,
        'm_queue_async' => \m_queue_async::class,
        'm_queue_base' => \m_queue_base::class,
        'm_queue_bbexport' => \m_queue_bbexport::class,
        'm_queue_bbimport' => \m_queue_bbimport::class,
        'm_queue_pzexport' => \m_queue_pzexport::class,
        'm_queue_pzimport' => \m_queue_pzimport::class,
        'm_queue_zdshenhe' => \m_queue_zdshenhe::class,
        'm_rw_bb' => \m_rw_bb::class,
        'm_rw_cronrwx' => \m_rw_cronrwx::class,
        'm_rw_import' => \m_rw_import::class,
        'm_rw_langai' => \m_rw_langai::class,
        'm_sanfang_base' => \m_sanfang_base::class,
        'm_sanfang_zz' => \m_sanfang_zz::class,
        'm_third_base' => \m_third_base::class,
        'm_third_masterx' => \m_third_masterx::class,
        'm_tongji_zjmx' => \m_tongji_zjmx::class,
        'm_utils_ali' => \m_utils_ali::class,
        'db' => \db::class,
    ]));

    // M() 函数映射
    override(\M(0), map([
        "" => \db::class,
        'select(0, 1, 2, 3)' => 'array',
        'get(0, 1, 2, 3)' => 'array|null',
        's_page(0, 1, 2, 3, 4, 5)' => 'array',
        'has(0, 1)' => 'bool',
        'insert(0, 1, 2)' => 'int|bool',
        'update(0, 1, 2, 3)' => 'bool',
        'count(0, 1)' => 'int',
    ]));

    // MM() 函数映射
    override(\MM(0), map([
        "" => \db::class,
    ]));
}
