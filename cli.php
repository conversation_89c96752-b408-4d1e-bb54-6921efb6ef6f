<?php
//sudo -Hu www php cli.php "main/aaa?aa=1&bb=2&cc=3"

php_sapi_name()!== 'cli' && die;
$argv = $_SERVER['argv'];
$_SERVER['DOCUMENT_ROOT'] = __DIR__;
$_SERVER['REQUEST_URI'] = '/'.$argv[1];
$_SERVER["SCRIPT_NAME"] = '/index.php';
$_SERVER['HTTP_HOST'] = 'localhost';
parse_str(parse_url($_SERVER['REQUEST_URI'],PHP_URL_QUERY),$_GET);

$app_name = "app";                  //项目目录名
define('TRACE',0);                  //关闭调试打印信息
define('SESSION_AUTO',0);           //关闭session

define('DS',DIRECTORY_SEPARATOR);   //路径分隔符
define('ROOT',__DIR__.DS);          //根目录
define('APP',ROOT.$app_name.DS);    //项目目录
define('JU',ROOT."ju".DS);          //框架目录
require APP.'config.php';           //项目配置文件
require JU.'ju.php';                //引入框架